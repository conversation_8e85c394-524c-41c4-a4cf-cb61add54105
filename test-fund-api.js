/**
 * Test script for CSRC Fund API
 * This API retrieves fund net value information from China Securities Regulatory Commission
 */

// URL decode the aoData parameter to understand the structure
function decodeAoData(encodedData) {
  const decoded = decodeURIComponent(encodedData);
  console.log('Decoded aoData:', decoded);
  
  try {
    const parsed = JSON.parse(decoded);
    console.log('Parsed aoData structure:');
    parsed.forEach((item, index) => {
      console.log(`  ${index}: ${item.name} = ${item.value}`);
    });
    return parsed;
  } catch (error) {
    console.error('Failed to parse aoData:', error);
    return null;
  }
}

// Build aoData parameter for the API request
function buildAoData(params = {}) {
  const defaultParams = {
    sEcho: 1,
    iColumns: 5,
    sColumns: ',,,',
    iDisplayStart: 0,
    iDisplayLength: 20,
    mDataProp_0: 'fund',
    mDataProp_1: 'fund', 
    mDataProp_2: 'fund',
    mDataProp_3: 'fund',
    mDataProp_4: 'valuationDate',
    fundType: 'all',
    fundCompanyShortName: '',
    fundCode: '',
    fundName: '',
    startDate: '2025-01-06',
    endDate: '2025-01-06'
  };

  const mergedParams = { ...defaultParams, ...params };
  
  const aoDataArray = Object.entries(mergedParams).map(([name, value]) => ({
    name,
    value
  }));

  return encodeURIComponent(JSON.stringify(aoDataArray));
}

// Make API request to fetch fund data
async function fetchFundData(params = {}) {
  const baseUrl = 'http://eid.csrc.gov.cn/fund/disclose/getPublicFundJZInfoMore.do';
  const aoData = buildAoData(params);
  const timestamp = Date.now();
  const url = `${baseUrl}?aoData=${aoData}&_=${timestamp}`;
  
  console.log('Request URL:', url);
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://eid.csrc.gov.cn/fund/disclose/publicFundJZInfo.do'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching fund data:', error);
    throw error;
  }
}

// Test different parameter combinations
async function testAPI() {
  console.log('=== Testing CSRC Fund API ===\n');

  // Test 1: Decode the original URL parameters
  console.log('1. Decoding original URL parameters:');
  const originalAoData = '%5B%7B%22name%22%3A%22sEcho%22%2C%22value%22%3A2%7D%2C%7B%22name%22%3A%22iColumns%22%2C%22value%22%3A5%7D%2C%7B%22name%22%3A%22sColumns%22%2C%22value%22%3A%22%2C%2C%2C%2C%22%7D%2C%7B%22name%22%3A%22iDisplayStart%22%2C%22value%22%3A20%7D%2C%7B%22name%22%3A%22iDisplayLength%22%2C%22value%22%3A20%7D%2C%7B%22name%22%3A%22mDataProp_0%22%2C%22value%22%3A%22fund%22%7D%2C%7B%22name%22%3A%22mDataProp_1%22%2C%22value%22%3A%22fund%22%7D%2C%7B%22name%22%3A%22mDataProp_2%22%2C%22value%22%3A%22fund%22%7D%2C%7B%22name%22%3A%22mDataProp_3%22%2C%22value%22%3A%22fund%22%7D%2C%7B%22name%22%3A%22mDataProp_4%22%2C%22value%22%3A%22valuationDate%22%7D%2C%7B%22name%22%3A%22fundType%22%2C%22value%22%3A%22all%22%7D%2C%7B%22name%22%3A%22fundCompanyShortName%22%2C%22value%22%3A%22%22%7D%2C%7B%22name%22%3A%22fundCode%22%2C%22value%22%3A%22%22%7D%2C%7B%22name%22%3A%22fundName%22%2C%22value%22%3A%22Y%22%7D%2C%7B%22name%22%3A%22startDate%22%2C%22value%22%3A%222025-06-06%22%7D%2C%7B%22name%22%3A%22endDate%22%2C%22value%22%3A%222025-06-06%22%7D%5D';
  decodeAoData(originalAoData);
  console.log('\n');

  // Test 2: Basic request - first 20 records
  console.log('2. Testing basic request (first 20 records):');
  try {
    const data1 = await fetchFundData();
    console.log(`Total records: ${data1.iTotalRecords}`);
    console.log(`Returned records: ${data1.aaData.length}`);
    if (data1.aaData.length > 0) {
      const firstFund = data1.aaData[0];
      console.log('First fund example:');
      console.log(`  Code: ${firstFund.code}`);
      console.log(`  Name: ${firstFund.shortName}`);
      console.log(`  Net Value: ${firstFund.shareNetValue}`);
      console.log(`  Total Net Value: ${firstFund.totalNetValue}`);
      console.log(`  Valuation Date: ${firstFund.valuationDate}`);
    }
  } catch (error) {
    console.error('Test 2 failed:', error.message);
  }
  console.log('\n');

  // Test 3: Pagination - get next 20 records
  console.log('3. Testing pagination (records 21-40):');
  try {
    const data2 = await fetchFundData({ iDisplayStart: 20, iDisplayLength: 20 });
    console.log(`Returned records: ${data2.aaData.length}`);
    if (data2.aaData.length > 0) {
      console.log('Sample fund from page 2:');
      console.log(`  Code: ${data2.aaData[0].code}`);
      console.log(`  Name: ${data2.aaData[0].shortName}`);
    }
  } catch (error) {
    console.error('Test 3 failed:', error.message);
  }
  console.log('\n');

  // Test 4: Search by fund name containing "沪深300"
  console.log('4. Testing search by fund name (containing "沪深300"):');
  try {
    const data3 = await fetchFundData({ fundName: '沪深300' });
    console.log(`Found records: ${data3.aaData.length}`);
    data3.aaData.slice(0, 3).forEach((fund, index) => {
      console.log(`  ${index + 1}. ${fund.code} - ${fund.shortName}`);
    });
  } catch (error) {
    console.error('Test 4 failed:', error.message);
  }
  console.log('\n');

  // Test 5: Search by specific fund code
  console.log('5. Testing search by fund code (110003):');
  try {
    const data4 = await fetchFundData({ fundCode: '110003' });
    console.log(`Found records: ${data4.aaData.length}`);
    if (data4.aaData.length > 0) {
      const fund = data4.aaData[0];
      console.log(`  Code: ${fund.code}`);
      console.log(`  Name: ${fund.shortName}`);
      console.log(`  Net Value: ${fund.shareNetValue}`);
    }
  } catch (error) {
    console.error('Test 5 failed:', error.message);
  }
  console.log('\n');

  // Test 6: Different date range
  console.log('6. Testing different date range (2024-12-31):');
  try {
    const data5 = await fetchFundData({ 
      startDate: '2024-12-31', 
      endDate: '2024-12-31',
      iDisplayLength: 5
    });
    console.log(`Found records: ${data5.aaData.length}`);
    data5.aaData.forEach((fund, index) => {
      console.log(`  ${index + 1}. ${fund.code} - ${fund.shortName} (${fund.valuationDate})`);
    });
  } catch (error) {
    console.error('Test 6 failed:', error.message);
  }
  console.log('\n');

  // Test 7: Large page size
  console.log('7. Testing large page size (50 records):');
  try {
    const data6 = await fetchFundData({ iDisplayLength: 50 });
    console.log(`Requested 50, got: ${data6.aaData.length} records`);
  } catch (error) {
    console.error('Test 7 failed:', error.message);
  }
}

// Run the tests
testAPI().catch(console.error);
