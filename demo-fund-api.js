/**
 * Demo script showing how the CSRC Fund API works
 * This demonstrates the correct usage patterns
 */

const {
  fetchFundData,
  searchFundsByName,
  getFundByCode,
  getAllFunds
} = require('./src/utils/csrc-fund-api');

async function demonstrateAPI() {
  console.log('=== CSRC Fund API Demo ===\n');

  // The API seems to be date-sensitive, so let's try different dates
  const testDates = [
    '2024-12-31',  // Last trading day of 2024
    '2024-12-30',  // Previous trading day
    '2024-12-27',  // Friday before
    '2025-01-06'   // Recent date
  ];

  for (const date of testDates) {
    console.log(`\n--- Testing with date: ${date} ---`);
    
    try {
      // Test 1: Get all funds for this date
      const allFunds = await getAllFunds(0, 5, date);
      console.log(`Total funds available on ${date}: ${allFunds.totalRecords}`);
      
      if (allFunds.funds.length > 0) {
        console.log('Sample funds:');
        allFunds.funds.forEach((fund, index) => {
          console.log(`  ${index + 1}. ${fund.code} - ${fund.name}`);
          console.log(`     Net Value: ${fund.shareNetValue || 'N/A'}`);
        });

        // Test 2: Try searching by name with funds that exist
        console.log('\nTesting search by name with "债券":');
        const bondFunds = await searchFundsByName('债券', 3);
        console.log(`Found ${bondFunds.length} bond funds`);
        bondFunds.forEach((fund, index) => {
          console.log(`  ${index + 1}. ${fund.code} - ${fund.name}`);
        });

        // Test 3: Try to get a specific fund
        if (allFunds.funds.length > 0) {
          const testCode = allFunds.funds[0].code;
          console.log(`\nTesting specific fund lookup for code: ${testCode}`);
          const specificFund = await getFundByCode(testCode, date);
          if (specificFund) {
            console.log(`  Found: ${specificFund.name}`);
            console.log(`  Net Value: ${specificFund.shareNetValue || 'N/A'}`);
          }
        }
        
        break; // Found working date, no need to test others
      } else {
        console.log(`No funds found for ${date}`);
      }
    } catch (error) {
      console.log(`Error testing ${date}: ${error.message}`);
    }
  }

  // Test raw API with different parameters
  console.log('\n--- Testing Raw API Parameters ---');
  
  const testParams = [
    { description: 'All funds, first 3', params: { iDisplayLength: 3 } },
    { description: 'Search for "Y" funds', params: { fundName: 'Y', iDisplayLength: 3 } },
    { description: 'Pagination test (skip 10)', params: { iDisplayStart: 10, iDisplayLength: 3 } },
    { description: 'Date range test', params: { startDate: '2024-12-31', endDate: '2024-12-31', iDisplayLength: 3 } }
  ];

  for (const test of testParams) {
    try {
      console.log(`\n${test.description}:`);
      const result = await fetchFundData(test.params);
      console.log(`  Total records: ${result.iTotalRecords}`);
      console.log(`  Returned: ${result.aaData.length} records`);
      
      result.aaData.forEach((fund, index) => {
        console.log(`    ${index + 1}. ${fund.code} - ${fund.shortName}`);
        console.log(`       Date: ${fund.valuationDate}, Value: ${fund.shareNetValue || 'N/A'}`);
      });
    } catch (error) {
      console.log(`  Error: ${error.message}`);
    }
  }

  // Show URL structure
  console.log('\n--- API URL Structure Analysis ---');
  console.log('The API uses a complex parameter structure called "aoData"');
  console.log('This is a JSON array encoded as a URL parameter');
  console.log('Each element has a "name" and "value" property');
  console.log('\nKey parameters:');
  console.log('- sEcho: Request sequence number');
  console.log('- iDisplayStart: Starting record (pagination)');
  console.log('- iDisplayLength: Number of records to return');
  console.log('- fundCode: Specific fund code to search');
  console.log('- fundName: Fund name to search (partial match)');
  console.log('- startDate/endDate: Date range for fund data');
  console.log('- fundType: Type of fund ("all" for all types)');
  
  console.log('\n--- Usage Recommendations ---');
  console.log('1. Always specify a date range - the API is date-sensitive');
  console.log('2. Use recent trading days (avoid weekends/holidays)');
  console.log('3. Start with small page sizes (20-50 records)');
  console.log('4. Fund names support partial matching');
  console.log('5. Some funds may not have net values on all dates');
  console.log('6. The API returns both A and C share classes for many funds');
}

// Run the demo
if (require.main === module) {
  demonstrateAPI().catch(console.error);
}

module.exports = { demonstrateAPI };
