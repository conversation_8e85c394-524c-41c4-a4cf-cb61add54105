/**
 * Complete examples of how to use the CSRC Fund API correctly
 * This shows all the working patterns discovered through testing
 */

const { fetchFundData } = require('./src/utils/csrc-fund-api');

/**
 * Example 1: Get all funds for a specific date with pagination
 */
async function getAllFundsExample() {
  console.log('=== Example 1: Get All Funds with Pagination ===');
  
  const date = '2024-12-31'; // Use a known trading day
  
  // Get first page
  const page1 = await fetchFundData({
    startDate: date,
    endDate: date,
    iDisplayStart: 0,
    iDisplayLength: 10
  });
  
  console.log(`Total funds on ${date}: ${page1.iTotalRecords}`);
  console.log(`First 10 funds:`);
  page1.aaData.forEach((fund, index) => {
    console.log(`  ${index + 1}. ${fund.code} - ${fund.shortName}`);
    console.log(`     Net Value: ${fund.shareNetValue || 'N/A'}, Date: ${fund.valuationDate}`);
  });
  
  // Get second page
  const page2 = await fetchFundData({
    startDate: date,
    endDate: date,
    iDisplayStart: 10,
    iDisplayLength: 10
  });
  
  console.log(`\nNext 10 funds:`);
  page2.aaData.forEach((fund, index) => {
    console.log(`  ${index + 11}. ${fund.code} - ${fund.shortName}`);
  });
}

/**
 * Example 2: Search for specific fund by code
 */
async function getFundByCodeExample() {
  console.log('\n=== Example 2: Get Specific Fund by Code ===');
  
  const fundCode = '007070'; // Use a code we know exists
  const date = '2024-12-31';
  
  const result = await fetchFundData({
    fundCode: fundCode,
    startDate: date,
    endDate: date,
    iDisplayLength: 5
  });
  
  console.log(`Search results for fund code ${fundCode}:`);
  result.aaData.forEach((fund, index) => {
    console.log(`  ${index + 1}. ${fund.code} - ${fund.shortName}`);
    console.log(`     Share Net Value: ${fund.shareNetValue || 'N/A'}`);
    console.log(`     Total Net Value: ${fund.totalNetValue || 'N/A'}`);
    console.log(`     Valuation Date: ${fund.valuationDate}`);
  });
}

/**
 * Example 3: Search funds by name (partial matching)
 */
async function searchFundsByNameExample() {
  console.log('\n=== Example 3: Search Funds by Name ===');
  
  const searchTerms = ['FOF', '养老', '混合'];
  const date = '2024-12-31';
  
  for (const term of searchTerms) {
    console.log(`\nSearching for funds containing "${term}":`);
    
    const result = await fetchFundData({
      fundName: term,
      startDate: date,
      endDate: date,
      iDisplayLength: 5
    });
    
    console.log(`Found ${result.iTotalRecords} total matches, showing first ${result.aaData.length}:`);
    result.aaData.forEach((fund, index) => {
      console.log(`  ${index + 1}. ${fund.code} - ${fund.shortName}`);
      console.log(`     Net Value: ${fund.shareNetValue || 'N/A'}`);
    });
  }
}

/**
 * Example 4: Get funds from different dates
 */
async function getHistoricalDataExample() {
  console.log('\n=== Example 4: Get Historical Data ===');
  
  const dates = ['2024-12-31', '2024-12-30', '2024-12-27'];
  const fundCode = '007070';
  
  console.log(`Historical data for fund ${fundCode}:`);
  
  for (const date of dates) {
    try {
      const result = await fetchFundData({
        fundCode: fundCode,
        startDate: date,
        endDate: date,
        iDisplayLength: 1
      });
      
      if (result.aaData.length > 0) {
        const fund = result.aaData[0];
        console.log(`  ${date}: ${fund.shareNetValue || 'N/A'} (${fund.shortName})`);
      } else {
        console.log(`  ${date}: No data available`);
      }
    } catch (error) {
      console.log(`  ${date}: Error - ${error.message}`);
    }
  }
}

/**
 * Example 5: Advanced search with multiple filters
 */
async function advancedSearchExample() {
  console.log('\n=== Example 5: Advanced Search ===');
  
  // Search for ETF funds
  console.log('Searching for ETF funds:');
  const etfResult = await fetchFundData({
    fundName: 'ETF',
    startDate: '2024-12-31',
    endDate: '2024-12-31',
    iDisplayLength: 5
  });
  
  console.log(`Found ${etfResult.iTotalRecords} ETF funds:`);
  etfResult.aaData.forEach((fund, index) => {
    console.log(`  ${index + 1}. ${fund.code} - ${fund.shortName}`);
  });
  
  // Search for index funds
  console.log('\nSearching for index funds (指数):');
  const indexResult = await fetchFundData({
    fundName: '指数',
    startDate: '2024-12-31',
    endDate: '2024-12-31',
    iDisplayLength: 5
  });
  
  console.log(`Found ${indexResult.iTotalRecords} index funds:`);
  indexResult.aaData.forEach((fund, index) => {
    console.log(`  ${index + 1}. ${fund.code} - ${fund.shortName}`);
  });
}

/**
 * Example 6: Bulk data collection
 */
async function bulkDataCollectionExample() {
  console.log('\n=== Example 6: Bulk Data Collection ===');
  
  const date = '2024-12-31';
  const pageSize = 50;
  let currentPage = 0;
  let allFunds = [];
  
  console.log('Collecting first 200 funds...');
  
  // Collect first 4 pages (200 funds total)
  while (currentPage < 4) {
    const result = await fetchFundData({
      startDate: date,
      endDate: date,
      iDisplayStart: currentPage * pageSize,
      iDisplayLength: pageSize
    });
    
    allFunds = allFunds.concat(result.aaData);
    console.log(`Collected page ${currentPage + 1}: ${result.aaData.length} funds`);
    
    currentPage++;
    
    // Add delay to be respectful to the API
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`\nTotal collected: ${allFunds.length} funds`);
  console.log('Sample of collected data:');
  allFunds.slice(0, 5).forEach((fund, index) => {
    console.log(`  ${index + 1}. ${fund.code} - ${fund.shortName} (${fund.shareNetValue || 'N/A'})`);
  });
  
  // Analyze the data
  const fundsWithValues = allFunds.filter(fund => fund.shareNetValue && fund.shareNetValue !== '');
  console.log(`\nFunds with net values: ${fundsWithValues.length}/${allFunds.length}`);
  
  const uniqueCodes = new Set(allFunds.map(fund => fund.code));
  console.log(`Unique fund codes: ${uniqueCodes.size}`);
}

/**
 * Main function to run all examples
 */
async function runAllExamples() {
  console.log('CSRC Fund API - Complete Usage Examples\n');
  console.log('This demonstrates all the working patterns for the API\n');
  
  try {
    await getAllFundsExample();
    await getFundByCodeExample();
    await searchFundsByNameExample();
    await getHistoricalDataExample();
    await advancedSearchExample();
    await bulkDataCollectionExample();
    
    console.log('\n=== Key Findings ===');
    console.log('1. The API requires explicit date ranges (startDate/endDate)');
    console.log('2. Without dates, most searches return 0 results');
    console.log('3. Use recent trading days (avoid weekends/holidays)');
    console.log('4. The API supports pagination with iDisplayStart/iDisplayLength');
    console.log('5. Fund name search supports partial matching');
    console.log('6. Many funds have multiple share classes (A, C, Y)');
    console.log('7. Some funds may not have net values on all dates');
    console.log('8. The API can handle bulk requests but add delays between calls');
    
  } catch (error) {
    console.error('Error running examples:', error);
  }
}

// Run examples if this file is executed directly
if (require.main === module) {
  runAllExamples();
}

module.exports = {
  getAllFundsExample,
  getFundByCodeExample,
  searchFundsByNameExample,
  getHistoricalDataExample,
  advancedSearchExample,
  bulkDataCollectionExample
};
