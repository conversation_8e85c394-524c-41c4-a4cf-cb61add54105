/**
 * Node.js test script for CSRC Fund API
 * Run with: node test-fund-api-node.js
 */

const https = require('https');
const http = require('http');

// Build aoData parameter for the API request
function buildAoData(params = {}) {
  const defaultParams = {
    sEcho: 1,
    iColumns: 5,
    sColumns: ',,,',
    iDisplayStart: 0,
    iDisplayLength: 20,
    mDataProp_0: 'fund',
    mDataProp_1: 'fund', 
    mDataProp_2: 'fund',
    mDataProp_3: 'fund',
    mDataProp_4: 'valuationDate',
    fundType: 'all',
    fundCompanyShortName: '',
    fundCode: '',
    fundName: '',
    startDate: '2025-01-06',
    endDate: '2025-01-06'
  };

  const mergedParams = { ...defaultParams, ...params };
  
  const aoDataArray = Object.entries(mergedParams).map(([name, value]) => ({
    name,
    value
  }));

  return encodeURIComponent(JSON.stringify(aoDataArray));
}

// Make HTTP request
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://eid.csrc.gov.cn/fund/disclose/publicFundJZInfo.do'
      }
    };

    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// Make API request to fetch fund data
async function fetchFundData(params = {}) {
  const baseUrl = 'http://eid.csrc.gov.cn/fund/disclose/getPublicFundJZInfoMore.do';
  const aoData = buildAoData(params);
  const timestamp = Date.now();
  const url = `${baseUrl}?aoData=${aoData}&_=${timestamp}`;
  
  console.log('Request URL length:', url.length);
  
  try {
    const data = await makeRequest(url);
    return data;
  } catch (error) {
    console.error('Error fetching fund data:', error.message);
    throw error;
  }
}

// URL decode function for analysis
function decodeAoData(encodedData) {
  const decoded = decodeURIComponent(encodedData);
  console.log('Decoded aoData:', decoded);
  
  try {
    const parsed = JSON.parse(decoded);
    console.log('Parsed aoData structure:');
    parsed.forEach((item, index) => {
      console.log(`  ${index}: ${item.name} = ${item.value}`);
    });
    return parsed;
  } catch (error) {
    console.error('Failed to parse aoData:', error);
    return null;
  }
}

// Test different parameter combinations
async function testAPI() {
  console.log('=== Testing CSRC Fund API ===\n');

  // Test 1: Decode the original URL parameters
  console.log('1. Decoding original URL parameters:');
  const originalAoData = '%5B%7B%22name%22%3A%22sEcho%22%2C%22value%22%3A2%7D%2C%7B%22name%22%3A%22iColumns%22%2C%22value%22%3A5%7D%2C%7B%22name%22%3A%22sColumns%22%2C%22value%22%3A%22%2C%2C%2C%2C%22%7D%2C%7B%22name%22%3A%22iDisplayStart%22%2C%22value%22%3A20%7D%2C%7B%22name%22%3A%22iDisplayLength%22%2C%22value%22%3A20%7D%2C%7B%22name%22%3A%22mDataProp_0%22%2C%22value%22%3A%22fund%22%7D%2C%7B%22name%22%3A%22mDataProp_1%22%2C%22value%22%3A%22fund%22%7D%2C%7B%22name%22%3A%22mDataProp_2%22%2C%22value%22%3A%22fund%22%7D%2C%7B%22name%22%3A%22mDataProp_3%22%2C%22value%22%3A%22fund%22%7D%2C%7B%22name%22%3A%22mDataProp_4%22%2C%22value%22%3A%22valuationDate%22%7D%2C%7B%22name%22%3A%22fundType%22%2C%22value%22%3A%22all%22%7D%2C%7B%22name%22%3A%22fundCompanyShortName%22%2C%22value%22%3A%22%22%7D%2C%7B%22name%22%3A%22fundCode%22%2C%22value%22%3A%22%22%7D%2C%7B%22name%22%3A%22fundName%22%2C%22value%22%3A%22Y%22%7D%2C%7B%22name%22%3A%22startDate%22%2C%22value%22%3A%222025-06-06%22%7D%2C%7B%22name%22%3A%22endDate%22%2C%22value%22%3A%222025-06-06%22%7D%5D';
  decodeAoData(originalAoData);
  console.log('\n');

  // Test 2: Basic request - first 20 records
  console.log('2. Testing basic request (first 20 records):');
  try {
    const data1 = await fetchFundData();
    console.log(`Total records: ${data1.iTotalRecords}`);
    console.log(`Returned records: ${data1.aaData.length}`);
    if (data1.aaData.length > 0) {
      const firstFund = data1.aaData[0];
      console.log('First fund example:');
      console.log(`  Code: ${firstFund.code}`);
      console.log(`  Name: ${firstFund.shortName}`);
      console.log(`  Net Value: ${firstFund.shareNetValue}`);
      console.log(`  Total Net Value: ${firstFund.totalNetValue}`);
      console.log(`  Valuation Date: ${firstFund.valuationDate}`);
    }
  } catch (error) {
    console.error('Test 2 failed:', error.message);
  }
  console.log('\n');

  // Test 3: Search by fund name containing "沪深300"
  console.log('3. Testing search by fund name (containing "沪深300"):');
  try {
    const data3 = await fetchFundData({ fundName: '沪深300' });
    console.log(`Found records: ${data3.aaData.length}`);
    data3.aaData.slice(0, 3).forEach((fund, index) => {
      console.log(`  ${index + 1}. ${fund.code} - ${fund.shortName}`);
    });
  } catch (error) {
    console.error('Test 3 failed:', error.message);
  }
  console.log('\n');

  // Test 4: Different page size
  console.log('4. Testing different page size (5 records):');
  try {
    const data4 = await fetchFundData({ iDisplayLength: 5 });
    console.log(`Requested 5, got: ${data4.aaData.length} records`);
    data4.aaData.forEach((fund, index) => {
      console.log(`  ${index + 1}. ${fund.code} - ${fund.shortName}`);
    });
  } catch (error) {
    console.error('Test 4 failed:', error.message);
  }
  console.log('\n');

  console.log('=== API Testing Complete ===');
}

// Run the tests
if (require.main === module) {
  testAPI().catch(console.error);
}

module.exports = { fetchFundData, buildAoData };
